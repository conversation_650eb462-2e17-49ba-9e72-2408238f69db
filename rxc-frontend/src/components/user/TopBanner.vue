<script setup lang="ts">
import { ref, computed } from 'vue'
import EditButton from '@/components/user/EditButton.vue'
import TopBannerEditModal from '@/components/user/TopBannerEditModal.vue'
import { Button } from '@/components/ui/button'
import { IconPlus } from '@tabler/icons-vue'

const props = defineProps({
  content: {
    type: Object,
    default: () => ({ items: [] }),
  },
  isAdmin: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:content'])

const isModalOpen = ref(false)

// Default banner items if no content is provided
const defaultItems = [
  { text: 'US sourced ingredients', icon: '@/assets/images/us-flag.png' },
  { text: 'Trusted by over 100K subscribers', icon: '@/assets/images/star.png' },
  { text: '100% online process', icon: '@/assets/images/laptop.png' },
  { text: 'No membership requirements', icon: '@/assets/images/user.png' },
  { text: 'Transparent pricing, no hidden fees', icon: '@/assets/images/no-hidden-fees.png' },
  { text: 'FDA regulated pharmacies', icon: '@/assets/images/prescription.png' },
  { text: 'Board certified physicians', icon: '@/assets/images/physician.png' },
]

const bannerItems = computed(() => {
  return props.content?.items?.length > 0 ? props.content.items : defaultItems
})

const onSave = (items: any[]) => {
  const newContent = { ...props.content, items }
  emit('update:content', newContent)
}

const addBannerItems = () => {
  isModalOpen.value = true
}
</script>

<template>
  <!-- Show banner if items exist or not in admin mode -->
  <div
    v-if="bannerItems.length > 0 || !isAdmin"
    class="bg-gradient-to-r from-gray-100 via-stone-100 to-gray-100 border-b border-gray-200/50 overflow-hidden py-2 z-50 relative group"
  >
    <div class="relative">
      <div class="flex animate-ticker whitespace-nowrap">
        <!-- First set of items -->
        <div class="flex items-center space-x-16 sm:space-x-28 pr-16 sm:pr-28 flex-shrink-0">
          <div
            v-for="(item, index) in bannerItems"
            :key="`first-${index}`"
            class="flex items-center space-x-2"
          >
            <img :src="item.icon" :alt="item.text" class="w-4 h-4 grayscale" />
            <span class="text-xs">{{ item.text }}</span>
          </div>
        </div>

        <!-- Duplicate set for seamless loop -->
        <div class="flex items-center space-x-16 sm:space-x-28 pr-16 sm:pr-28 flex-shrink-0">
          <div
            v-for="(item, index) in bannerItems"
            :key="`second-${index}`"
            class="flex items-center space-x-2"
          >
            <img :src="item.icon" :alt="item.text" class="w-4 h-4 grayscale" />
            <span class="text-xs">{{ item.text }}</span>
          </div>
        </div>
      </div>
    </div>
    <EditButton :is-admin="isAdmin" @edit="isModalOpen = true" />
  </div>

  <!-- Show "Add Banner Items" button when no items exist and in admin mode -->
  <div
    v-else-if="isAdmin && (!content?.items || content.items.length === 0)"
    class="bg-gradient-to-r from-gray-100 via-stone-100 to-gray-100 border-b border-gray-200/50 py-4 z-50 relative group"
  >
    <div class="flex justify-center">
      <Button variant="outline" @click="addBannerItems" class="text-sm">
        <IconPlus class="w-4 h-4 mr-2" />
        Add Banner Items
      </Button>
    </div>
  </div>

  <TopBannerEditModal
    :is-open="isModalOpen"
    :items="content?.items || []"
    @close="isModalOpen = false"
    @save="onSave"
  />
</template>

<style scoped>
@keyframes ticker {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-ticker {
  animation: ticker 30s linear infinite;
}

/* Pause animation on hover for better UX */
.animate-ticker:hover {
  animation-play-state: paused;
}

@media screen and (max-width: 767px) {
  .animate-ticker {
    animation: ticker 20s linear infinite;
  }
}

@media screen and (min-width: 1024px) {
  .animate-ticker {
    animation: ticker 40s linear infinite;
  }
}
</style>
