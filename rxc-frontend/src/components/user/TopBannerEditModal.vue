<script setup lang="ts">
import { ref, watch } from 'vue'
import { Button } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { IconPlus, IconTrash } from '@tabler/icons-vue'

type BannerItem = { text: string; icon: string }

const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true,
  },
  items: {
    type: Array as () => BannerItem[],
    required: true,
  },
})

const emit = defineEmits(['close', 'save'])

const editableItems = ref(JSON.parse(JSON.stringify(props.items)))

watch(
  () => props.items,
  (newVal) => {
    editableItems.value = JSON.parse(JSON.stringify(newVal))
  },
)

const addItem = () => {
  editableItems.value.push({ text: '', icon: '' })
}

const removeItem = (index: number) => {
  editableItems.value.splice(index, 1)
}

const closeModal = () => {
  emit('close')
}

const save = () => {
  emit('save', editableItems.value)
  closeModal()
}
</script>

<template>
  <Dialog :open="isOpen" @update:open="closeModal">
    <DialogContent class="max-w-3xl">
      <DialogHeader>
        <DialogTitle>Edit Top Banner Items</DialogTitle>
      </DialogHeader>
      <div class="grid gap-4 py-4 max-h-[60vh] overflow-y-auto">
        <div
          v-for="(item, index) in editableItems"
          :key="index"
          class="flex items-start gap-2 p-4 border rounded-lg"
        >
          <div class="grid gap-2 flex-1">
            <Label :for="`text-${index}`">Text</Label>
            <Input :id="`text-${index}`" v-model="item.text" placeholder="Enter banner text" />
            <Label :for="`icon-${index}`">Icon Path</Label>
            <Input :id="`icon-${index}`" v-model="item.icon" placeholder="e.g., @/assets/images/icon.png" />
          </div>
          <Button variant="destructive" size="icon" @click="removeItem(index)">
            <IconTrash class="w-4 h-4" />
          </Button>
        </div>
        <Button variant="outline" @click="addItem">
          <IconPlus class="w-4 h-4 mr-2" />
          Add Banner Item
        </Button>
      </div>
      <DialogFooter>
        <Button variant="outline" @click="closeModal">Cancel</Button>
        <Button @click="save">Save</Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
